import { projectSchema } from '$lib/schemas/project';
import { fail } from '@sveltejs/kit';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod } from 'sveltekit-superforms/adapters';
import type { Actions, PageServerLoad } from './$types';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';
import { StandardRibaStages } from '$lib/project_utils';

export const load: PageServerLoad = async ({ locals, cookies, params }) => {
	await requireUser(cookies);

	const { supabase } = locals;

	// check if user is a client admin
	const { client_name, org_name } = params;
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('client_id, organization(name)')
		.eq('organization.name', org_name)
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (clientError) {
		console.error('Error fetching client:', clientError);
		return redirect(
			`/org/${org_name}/clients`,
			{ type: 'error', message: 'Something went wrong trying to access that client.' },
			cookies,
		);
	}

	if (!client) {
		return redirect('/', { type: 'error', message: 'Client not found.' }, cookies);
	}

	const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (isAdminError) {
		console.error('Error checking client admin status:', isAdminError);
	}

	if (!isAdmin) {
		return redirect(
			`/org/${org_name}/clients`,
			{ type: 'error', message: 'You do not have permission to create a project.' },
			cookies,
		);
	}

	// Get WBS libraries for dropdown
	const { data: wbsLibraries, error: wbsLibrariesError } = await supabase
		.from('wbs_library')
		.select('wbs_library_id, name')
		.order('name');

	if (wbsLibrariesError) {
		console.error('Error fetching WBS libraries:', wbsLibrariesError);
	}

	// Create a form using the project schema with default values
	const form = await superValidate(
		{
			selected_stages: StandardRibaStages.map((stage) => stage.stage_order), // All stages selected by default
		},
		zod(projectSchema),
		{ errors: false },
	);

	return {
		form,
		wbsLibraries: wbsLibraries || [],
	};
};

export const actions: Actions = {
	default: async ({ request, locals, params, cookies }) => {
		const { supabase } = locals;

		// Validate form data
		const form = await superValidate(request, zod(projectSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			// Check if user is logged in
			const { user } = await requireUser(cookies);

			// Get the user ID
			const userId = user.id;

			// get the client
			const { data: client, error: clientError } = await supabase
				.from('client')
				.select('client_id')
				.eq('name', params.client_name)
				.limit(1)
				.maybeSingle();

			if (clientError || !client) {
				console.error('Error fetching client:', clientError);
				return message(form, { type: 'error', text: 'Error fetching client.' }, { status: 500 });
			}

			// Insert the project
			const { error: insertError } = await supabase.from('project').insert({
				name: form.data.name,
				description: form.data.description || null,
				client_id: client.client_id,
				wbs_library_id: form.data.wbs_library_id,
				created_by_user_id: userId,
			});

			console.log({ insertError });

			if (insertError) {
				console.error('Error creating project:', insertError);
				return message(form, { type: 'error', text: 'Error creating project.' }, { status: 400 });
			}
		} catch (error) {
			console.error('Error in project creation:', error);
			return message(
				form,
				{ type: 'error', text: 'An unexpected error occurred.' },
				{ status: 500 },
			);
		}

		// Get the created project
		const { data: project, error: projectError } = await supabase
			.from('project')
			.select('project_id')
			.eq('name', form.data.name)
			.limit(1)
			.maybeSingle();

		if (projectError || !project) {
			console.error('Error fetching project:', projectError);
			return message(
				form,
				{ type: 'error', text: 'Error creating project stages.' },
				{ status: 500 },
			);
		}

		// Insert only the selected stages
		const selectedStages = StandardRibaStages.filter((stage) =>
			form.data.selected_stages.includes(stage.stage_order),
		);

		const { error: stagesError } = await supabase.from('project_stage').insert(
			selectedStages.map((stage) => ({
				...stage,
				project_id: project.project_id,
			})),
		);

		if (stagesError) {
			console.error('Error inserting project stages:', stagesError);
			return message(
				form,
				{ type: 'error', text: 'Error creating project stages.' },
				{ status: 500 },
			);
		}

		// Redirect based on whether user wants to import from CostX
		if (form.data.import_from_costx) {
			return redirect(
				`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(params.client_name)}/projects/${encodeURIComponent(form.data.name)}/budget/import`,
				{
					type: 'success',
					message: 'Project created successfully. You can now import your CostX budget.',
				},
				cookies,
			);
		} else {
			return redirect(
				`/org/${encodeURIComponent(params.org_name)}/clients/${encodeURIComponent(params.client_name)}/projects/${encodeURIComponent(form.data.name)}`,
				{ type: 'success', message: 'Project created successfully.' },
				cookies,
			);
		}
	},
};
