<script lang="ts">
	import { Input } from '$lib/components/ui/input';
	import * as Form from '$lib/components/ui/form';
	import { Textarea } from '$lib/components/ui/textarea';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { RadioGroup, RadioGroupItem } from '$lib/components/ui/radio-group';
	import { superForm } from 'sveltekit-superforms';
	import type { PageData } from './$types';
	import { toast } from 'svelte-sonner';
	import { StandardRibaStages } from '$lib/project_utils';

	const { data }: { data: PageData } = $props();

	const formHandler = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form, enhance } = formHandler;

	function addStage(stageOrder: number) {
		$form.selected_stages = [...$form.selected_stages, stageOrder];
	}

	function removeStage(stageOrder: number) {
		$form.selected_stages = $form.selected_stages.filter((s) => s !== stageOrder);
	}
</script>

<div class="container mx-auto max-w-2xl py-8">
	<h1>Create a Project</h1>

	<div class="rounded-lg border p-6 shadow-xs">
		<form method="POST" use:enhance>
			<div class="space-y-6">
				<!-- Project Name -->
				<Form.Field form={formHandler} name="name">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Project Name</Form.Label>
							<Input {...props} bind:value={$form.name} required placeholder="Enter project name" />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- Description -->
				<Form.Field form={formHandler} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description (optional)</Form.Label>
							<Textarea
								{...props}
								placeholder="Brief description of the project"
								class="resize-none"
								bind:value={$form.description}
							/>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- CostX Import Question -->
				<Form.Field form={formHandler} name="import_from_costx">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Budget Import</Form.Label>
							<Form.Description class="mb-3">
								Do you want to import a budget from CostX? If yes, you'll be able to upload your
								CostX export file after creating the project.
							</Form.Description>
							<RadioGroup
								{...props}
								value={$form.import_from_costx ? 'yes' : 'no'}
								onValueChange={(value: string) => {
									$form.import_from_costx = value === 'yes';
									// Set Custom WBS library (ID 1) when switching to CostX import
									if (value === 'yes') {
										$form.wbs_library_id = 1; // Custom WBS library
									}
									// Note: When switching back to manual selection, we keep the current value
									// The user will need to select a WBS library from the dropdown
								}}
								class="flex flex-col space-y-2"
							>
								<div class="flex items-center space-x-2">
									<RadioGroupItem value="no" id="no-costx" />
									<Form.Label for="no-costx" class="cursor-pointer font-normal">
										No, I'll select a WBS library
									</Form.Label>
								</div>
								<div class="flex items-center space-x-2">
									<RadioGroupItem value="yes" id="yes-costx" />
									<Form.Label for="yes-costx" class="cursor-pointer font-normal">
										Yes, import from CostX
									</Form.Label>
								</div>
							</RadioGroup>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<!-- WBS Library Selection (conditional) -->
				{#if !$form.import_from_costx}
					<Form.Field form={formHandler} name="wbs_library_id">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>WBS Library</Form.Label>
								<select
									{...props}
									bind:value={$form.wbs_library_id}
									class="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
								>
									<option selected disabled hidden value={undefined}>Select a WBS library</option>
									{#each data.wbsLibraries as library (library.wbs_library_id)}
										<option value={library.wbs_library_id}>{library.name}</option>
									{/each}
								</select>
							{/snippet}
						</Form.Control>
						<Form.Description>
							Select a Work Breakdown Structure library for this project
						</Form.Description>
						<Form.FieldErrors />
					</Form.Field>
				{/if}

				<!-- Stage Selection -->
				<Form.Fieldset form={formHandler} name="selected_stages" class="space-y-0">
					<div class="mb-4">
						<Form.Legend class="text-base">Project Stages</Form.Legend>
						<Form.Description>
							Select which stages should be included in this project. You can also customize stages
							later.
						</Form.Description>
					</div>
					<div class="space-y-3">
						{#each StandardRibaStages as stage (stage.stage_order)}
							{@const checked = $form.selected_stages.includes(stage.stage_order)}
							<div class="flex items-start space-x-3">
								<Form.Control>
									{#snippet children({ props })}
										<Checkbox
											{...props}
											{checked}
											value={stage.stage_order.toString()}
											onCheckedChange={(v) => {
												if (v) {
													addStage(stage.stage_order);
												} else {
													removeStage(stage.stage_order);
												}
											}}
										/>
										<Form.Label class="cursor-pointer text-sm leading-none font-normal">
											Stage {stage.stage_order}: {stage.name}
										</Form.Label>
									{/snippet}
								</Form.Control>
								{#if stage.description}
									<div class="ml-6 grid gap-1.5 leading-none">
										<p class="text-muted-foreground text-xs">
											{stage.description.split('\n')[0]}
										</p>
									</div>
								{/if}
							</div>
						{/each}
						<Form.FieldErrors />
					</div>
				</Form.Fieldset>

				<div class="pt-4">
					<Form.Button class="w-full">Create Project</Form.Button>
				</div>
			</div>
		</form>
	</div>
</div>
